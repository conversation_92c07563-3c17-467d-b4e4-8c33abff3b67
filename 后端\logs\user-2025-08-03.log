[2025-08-03T06:52:57.629Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:52:57.631Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:52:57.631Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:52:57.633Z] 购物车API - 读取到的购物车数据，商品数量: 7
[2025-08-03T06:52:57.633Z] 购物车API - 返回购物车数据，商品数量: 7
[2025-08-03T06:53:05.165Z] 保存用户购物车: 1111, 商品数量: 6
[2025-08-03T06:53:05.664Z] 保存用户购物车: 1111, 商品数量: 5
[2025-08-03T06:53:06.184Z] 保存用户购物车: 1111, 商品数量: 4
[2025-08-03T06:53:08.801Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T06:53:09.041Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T06:53:09.248Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T06:53:09.480Z] 保存用户购物车: 1111, 商品数量: 0
[2025-08-03T06:53:10.795Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:53:10.796Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:53:10.797Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:53:10.798Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T06:53:10.799Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T06:53:13.841Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:53:13.842Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:53:13.842Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:53:13.843Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T06:53:13.844Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T06:54:18.785Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T06:54:19.818Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:19.820Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:19.821Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:19.821Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T06:54:19.822Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T06:54:20.440Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:20.441Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:20.442Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:20.443Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T06:54:20.443Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T06:54:25.497Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:25.499Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:25.500Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:25.501Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T06:54:25.502Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T06:54:28.026Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T06:54:28.863Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:28.865Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:28.865Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:28.866Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T06:54:28.867Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T06:54:29.805Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:29.806Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:29.807Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:29.807Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T06:54:29.808Z] 购物车API - 返回购物车数据，商品数量: 2
