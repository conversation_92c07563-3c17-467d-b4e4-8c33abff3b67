<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的商品 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        header {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo-img {
            width: 45px;
            height: 45px;
            margin-right: 10px;
            border-radius: 8px;
        }

        .logo-text h1 {
            font-size: 22px;
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 12px;
            color: #666;
        }

        .gold {
            color: #d4af37;
        }

        /* 欢迎信息和用户下拉菜单相关样式已被移除 */

        /* Header Layout */
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #333;
        }

        .header-title h1 {
            font-size: 22px;
            margin-bottom: 2px;
            color: #0c4da2;
        }

        .header-title h1 i {
            color: #ffd700;
            margin-right: 8px;
        }

        .header-title p {
            font-size: 12px;
            color: #666;
            margin: 0;
        }

        .back-button {
            background: linear-gradient(135deg, #0c4da2 0%, #1e5bb8 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(12, 77, 162, 0.3);
            font-size: 14px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(12, 77, 162, 0.4);
        }

        .back-button i {
            margin-right: 8px;
        }

        /* Header Right Section */
        .header-right {
            display: flex;
            align-items: center;
        }

        /* Main Content */
        main {
            padding: 30px 0;
            min-height: calc(100vh - 150px);
        }

        /* 新设计 - 选项卡样式 */
        .tabs {
            display: flex;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .tab {
            flex: 1;
            text-align: center;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.8);
            position: relative;
        }

        .tab.active {
            background-color: rgba(255, 255, 255, 0.95);
            color: #0c4da2;
        }

        .tab i {
            margin-right: 8px;
            font-size: 18px;
        }

        .tab-content {
            display: none;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            animation: fadeIn 0.5s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 商品项目样式 */
        .product-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .product-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
            transition: box-shadow 0.2s ease;
            position: relative;
            display: flex;
            align-items: center;
            padding: 16px;
            gap: 16px;
            overflow: hidden;
        }

        .product-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 6px;
            flex-shrink: 0;
            border: 1px solid #e5e7eb;
        }

        .product-details {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .product-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.3;
            margin: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .product-price {
            font-size: 18px;
            font-weight: bold;
            color: #dc2626;
            margin: 0;
        }

        .product-description {
            font-size: 13px;
            color: #6b7280;
            margin: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .product-status {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .status-cart {
            background-color: #ffeed9;
            color: #ff8c00;
        }

        .status-paid {
            background-color: #e5f7ed;
            color: #28a745;
        }

        .status-shipped {
            background-color: #e3f2fd;
            color: #0c63e4;
        }

        .product-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
        }

        .btn-primary {
            background-color: #0c4da2;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0b3d80;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #bb2d3b;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        .btn-warning {
            background-color: #fd7e14;
            color: white;
        }

        .btn-warning:hover {
            background-color: #e76b00;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 22px;
            margin-bottom: 10px;
            color: #333;
        }

        .empty-state p {
            font-size: 16px;
            max-width: 400px;
            margin: 0 auto;
        }



        /* 总结部分样式 */
        .summary {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .summary-title {
            font-size: 18px;
            font-weight: bold;
            color: #0c4da2;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #eee;
        }

        .summary-row:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .summary-label {
            color: #666;
        }

        .summary-value {
            font-weight: bold;
            color: #333;
        }

        .summary-total {
            font-size: 18px;
            color: #d4af37;
        }

        /* 结算按钮 */
        .checkout-btn {
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
            color: #8b6914;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            margin-top: 20px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
        }

        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(212, 175, 55, 0.4);
        }

        .checkout-btn i {
            margin-right: 8px;
        }

        /* 数量控制样式 */
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 4px 0;
        }

        .quantity-btn {
            width: 24px;
            height: 24px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .quantity-btn:hover {
            background: #0c4da2;
            color: white;
            border-color: #0c4da2;
        }

        .quantity-display {
            min-width: 30px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            color: #374151;
        }

        /* 新增的布局样式 */
        .product-info-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 12px;
        }

        .product-controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;
            margin-top: 4px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .product-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .product-image {
                width: 60px;
                height: 60px;
                align-self: center;
            }

            .product-info-row {
                flex-direction: column;
                gap: 4px;
            }

            .product-controls-row {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .quantity-controls {
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .product-card {
                padding: 12px;
            }

            .product-name {
                font-size: 14px;
            }

            .product-price {
                font-size: 16px;
            }

            .btn {
                padding: 4px 8px;
                font-size: 12px;
            }
        }

        /* Footer */
        footer {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }

        footer .container {
            max-width: 500px;
        }

        footer p {
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-title h1 {
                font-size: 20px;
            }

            .header-title p {
                font-size: 12px;
            }
            
            .product-list {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }

        @media (max-width: 576px) {
            .header-title {
                display: none;
            }
            
            /* 欢迎信息样式已被移除 */
            
            .product-list {
                grid-template-columns: 1fr;
            }
            
            .tab {
                padding: 10px 5px;
                font-size: 14px;
            }
            
            .tab i {
                margin-right: 5px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-left">
                <div class="logo">
                    <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                    <div class="logo-text">
                        <h1><span class="gold">金舟</span>国际物流</h1>
                        <p>Jin Zhou International Logistics</p>
                    </div>
                </div>
                
                <!-- 欢迎用户按钮已被移除 -->
            </div>

            <div class="header-title">
                <h1><i class="fas fa-box-open"></i>我的商品</h1>
                <p>管理您的商品信息和状态</p>
            </div>

            <div class="header-right">
                <a href="recommend.html" class="back-button">
                    <i class="fas fa-arrow-left"></i>返回推荐
                </a>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <!-- 选项卡导航 -->
            <div class="tabs">
                <div class="tab active" data-tab="cart">
                    <i class="fas fa-shopping-cart"></i>购物车
                </div>
                <div class="tab" data-tab="paid">
                    <i class="fas fa-check-circle"></i>已付款商品
                </div>
            </div>
            
            <!-- 购物车内容 -->
            <div class="tab-content active" id="cart-content">
                <div class="product-list" id="cart-items">
                    <!-- 购物车为空的提示 -->
                    <div class="empty-state">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>您的购物车是空的</h3>
                        <p>浏览商品推荐，将心仪的商品添加到购物车</p>
                    </div>
                    
                    <!-- 购物车项目示例 -->
                    <!-- 
                    <div class="product-card">
                        <span class="counter">1</span>
                        <img src="img/product1.jpg" alt="商品图片" class="product-image">
                        <div class="product-details">
                            <span class="product-status status-cart">购物车</span>
                            <h3 class="product-name">高品质旅行箱</h3>
                            <div class="product-price">¥ 399.00</div>
                            <p class="product-description">耐用轻便的旅行箱，适合各种旅行场景，多种颜色可选。</p>
                            <div class="product-actions">
                                <button class="btn btn-danger">删除</button>
                                <button class="btn btn-success">立即购买</button>
                            </div>
                        </div>
                    </div>
                    -->
                </div>
                
                <!-- 购物车结算区域 -->
                <div class="summary" id="cart-summary">
                    <h3 class="summary-title">购物车汇总</h3>
                    <div class="summary-row">
                        <span class="summary-label">商品数量</span>
                        <span class="summary-value" id="cart-item-count">0</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">商品总价</span>
                        <span class="summary-value summary-total" id="cart-total-price">¥ 0.00</span>
                    </div>
                    <button class="checkout-btn" id="checkout-btn">
                        <i class="fas fa-credit-card"></i>结算全部
                    </button>
                </div>
            </div>
            
            <!-- 已付款商品内容 -->
            <div class="tab-content" id="paid-content">
                <div class="product-list" id="paid-items">
                    <!-- 没有已付款商品的提示 -->
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h3>暂无已付款商品</h3>
                        <p>您的已付款商品将显示在这里</p>
                    </div>
                    
                    <!-- 已付款商品示例 -->
                    <!--
                    <div class="product-card">
                        <img src="img/product2.jpg" alt="商品图片" class="product-image">
                        <div class="product-details">
                            <span class="product-status status-shipped">已发货</span>
                            <h3 class="product-name">智能手表</h3>
                            <div class="product-price">¥ 899.00</div>
                            <p class="product-description">多功能智能手表，支持心率监测、运动跟踪等功能。</p>
                            <div class="product-actions">
                                <button class="btn btn-primary">查看详情</button>
                                <button class="btn btn-warning">查询物流</button>
                            </div>
                        </div>
                    </div>
                    -->
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container" style="max-width: 800px;">
            <p>&copy; 2023 <span class="gold">金舟</span>国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // 检查用户登录状态
        document.addEventListener('DOMContentLoaded', function() {
            // 欢迎用户按钮已被移除，相关变量也已移除
            
            // 选项卡切换逻辑
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // 移除所有选项卡的活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    // 给当前选项卡添加活动状态
                    tab.classList.add('active');
                    
                    // 隐藏所有内容
                    tabContents.forEach(content => content.classList.remove('active'));
                    // 显示对应内容
                    const tabId = tab.getAttribute('data-tab');
                    document.getElementById(`${tabId}-content`).classList.add('active');
                });
            });
            
            // 安全地获取登录信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data from sessionStorage:', error);
                sessionStorage.removeItem('loggedInUser');
            }

            // 检查用户是否登录，如果未登录则重定向到登录页面
            if (!loggedInUser || !loggedInUser.isLoggedIn || !loggedInUser.username) {
                // 用户未登录，重定向到登录页面
                window.location.href = 'login.html?returnUrl=' + encodeURIComponent(window.location.href);
                return;
            }

            // 用户已登录，更新欢迎信息

            // 检查用户是否被拉黑（如果BlacklistHandler存在）
            if (typeof BlacklistHandler !== 'undefined') {
                BlacklistHandler.checkUserBlacklisted();
                // 定期检查用户状态
                setInterval(BlacklistHandler.checkUserBlacklisted, 30000);
            }

            // 安全地更新欢迎信息
            const loginStatusText = document.querySelector('.login-status-text');
            if (loginStatusText) {
                loginStatusText.textContent = '欢迎，' + loggedInUser.username;
            }

            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.classList.remove('not-logged-in');
                welcomeMessage.style.cursor = 'pointer';
            }

            // 更新下拉菜单中的用户名
            const accountName = document.querySelector('.account-name');
            if (accountName) {
                accountName.textContent = "我的账户";
            }

            // 为"我的账户"添加点击事件，跳转到用户中心
            const accountInfo = document.querySelector('.account-info');
            if (accountInfo) {
                accountInfo.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = 'dashboard.html';
                });
            }

            // 处理退出登录
            const logoutButton = document.querySelector('.logout-button');
            if (logoutButton) {
                logoutButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    sessionStorage.removeItem('loggedInUser');
                    window.location.href = 'recommend.html';
                });
            }
            
            // 初始化购物车管理器
            try {
                window.cartManager = new CartManager();
                window.cartManager.loadCart();

                // 结算按钮事件
                const checkoutBtn = document.getElementById('checkout-btn');
                if (checkoutBtn) {
                    checkoutBtn.addEventListener('click', function() {
                        window.cartManager.checkout();
                    });
                }
            } catch (error) {
                console.error('初始化购物车管理器失败:', error);
            }
        });

        // 购物车管理器类
        class CartManager {
            constructor() {
                this.cart = [];
                this.currentUser = null;
                this.init();
            }

            init() {
                // 获取当前登录用户
                try {
                    const userDataStr = sessionStorage.getItem('loggedInUser');
                    if (userDataStr) {
                        this.currentUser = JSON.parse(userDataStr);
                    }
                } catch (error) {
                    console.warn('Failed to parse user data:', error);
                }
            }

            // 加载购物车数据
            async loadCart() {
                if (!this.currentUser || !this.currentUser.username) {
                    this.renderEmptyCart();
                    return;
                }

                try {
                    const response = await fetch(`/api/cart/${encodeURIComponent(this.currentUser.username)}`);
                    const data = await response.json();

                    if (data.success) {
                        this.cart = data.cart || [];
                        this.renderCart();
                    } else {
                        this.renderEmptyCart();
                    }
                } catch (error) {
                    console.error('加载购物车失败:', error);
                    this.renderEmptyCart();
                }
            }

            // 添加商品到购物车
            async addToCart(productId, productData = null) {
                if (!this.currentUser || !this.currentUser.username) {
                    alert('请先登录后再添加商品到购物车');
                    return false;
                }

                try {
                    // 如果没有提供商品数据，从服务器获取
                    if (!productData) {
                        const productResponse = await fetch(`/api/products/${productId}`);
                        const productResult = await productResponse.json();

                        if (!productResult.success || !productResult.product) {
                            alert('商品信息获取失败');
                            return false;
                        }

                        productData = productResult.product;
                    }

                    // 检查商品是否已在购物车中
                    const existingItem = this.cart.find(item => item.productId === productId);

                    if (existingItem) {
                        // 如果已存在，增加数量
                        existingItem.quantity += 1;
                    } else {
                        // 如果不存在，添加新项目
                        this.cart.push({
                            productId: productId,
                            name: productData.name,
                            price: parseFloat(productData.price),
                            image: productData.mainImage || (productData.images && productData.images.length > 0 ? productData.images[0].url : ''),
                            description: productData.description || '',
                            quantity: 1,
                            addedAt: new Date().toISOString()
                        });
                    }

                    // 保存到服务器
                    await this.saveCart();

                    // 重新渲染购物车
                    this.renderCart();

                    // 显示成功消息
                    this.showMessage('商品已添加到购物车！', 'success');

                    return true;
                } catch (error) {
                    console.error('添加到购物车失败:', error);
                    alert('添加到购物车失败，请稍后重试');
                    return false;
                }
            }

            // 从购物车移除商品
            async removeFromCart(productId) {
                this.cart = this.cart.filter(item => item.productId !== productId);
                await this.saveCart();
                this.renderCart();
                this.showMessage('商品已从购物车移除', 'info');
            }

            // 更新商品数量
            async updateQuantity(productId, quantity) {
                const item = this.cart.find(item => item.productId === productId);
                if (item) {
                    if (quantity <= 0) {
                        await this.removeFromCart(productId);
                    } else {
                        item.quantity = quantity;
                        await this.saveCart();
                        this.renderCart();
                    }
                }
            }

            // 保存购物车到服务器
            async saveCart() {
                if (!this.currentUser || !this.currentUser.username) {
                    return;
                }

                try {
                    const response = await fetch('/api/cart/save', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: this.currentUser.username,
                            cart: this.cart
                        })
                    });

                    const data = await response.json();
                    if (!data.success) {
                        console.error('保存购物车失败:', data.message);
                    }
                } catch (error) {
                    console.error('保存购物车失败:', error);
                }
            }

            // 渲染购物车
            renderCart() {
                const cartItemsContainer = document.getElementById('cart-items');
                const cartSummary = document.getElementById('cart-summary');

                if (this.cart.length === 0) {
                    this.renderEmptyCart();
                    return;
                }

                // 渲染购物车项目
                const cartItemsHtml = this.cart.map(item => `
                    <div class="product-card" data-product-id="${item.productId}">
                        <img src="${item.image || 'https://via.placeholder.com/80x80/f0f0f0/999999?text=暂无图片'}"
                             alt="${item.name}" class="product-image"
                             onerror="this.src='https://via.placeholder.com/80x80/f0f0f0/999999?text=暂无图片'">
                        <div class="product-details">
                            <div class="product-info-row">
                                <h3 class="product-name" title="${item.name}">${item.name}</h3>
                                <div class="product-price">¥ ${item.price.toFixed(2)}</div>
                            </div>
                            <p class="product-description" title="${item.description}">${item.description.substring(0, 50)}${item.description.length > 50 ? '...' : ''}</p>
                            <div class="product-controls-row">
                                <div class="quantity-controls">
                                    <button class="quantity-btn" onclick="window.cartManager.updateQuantity('${item.productId}', ${item.quantity - 1})">-</button>
                                    <span class="quantity-display">${item.quantity}</span>
                                    <button class="quantity-btn" onclick="window.cartManager.updateQuantity('${item.productId}', ${item.quantity + 1})">+</button>
                                </div>
                                <div class="product-actions">
                                    <button class="btn btn-danger" onclick="window.cartManager.removeFromCart('${item.productId}')">删除</button>
                                    <button class="btn btn-success" onclick="window.cartManager.buyNow('${item.productId}')">立即购买</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');

                cartItemsContainer.innerHTML = cartItemsHtml;

                // 更新购物车汇总
                this.updateCartSummary();

                // 显示购物车汇总
                cartSummary.style.display = 'block';
            }

            // 渲染空购物车
            renderEmptyCart() {
                const cartItemsContainer = document.getElementById('cart-items');
                const cartSummary = document.getElementById('cart-summary');

                cartItemsContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>您的购物车是空的</h3>
                        <p>浏览商品推荐，将心仪的商品添加到购物车</p>
                    </div>
                `;

                // 隐藏购物车汇总
                cartSummary.style.display = 'none';
            }

            // 更新购物车汇总
            updateCartSummary() {
                const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
                const totalPrice = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

                document.getElementById('cart-item-count').textContent = totalItems;
                document.getElementById('cart-total-price').textContent = `¥ ${totalPrice.toFixed(2)}`;
            }

            // 立即购买单个商品
            buyNow(productId) {
                const item = this.cart.find(item => item.productId === productId);
                if (item) {
                    // 跳转到支付页面，传递单个商品信息
                    window.location.href = `payment.html?type=single&productId=${encodeURIComponent(productId)}`;
                }
            }

            // 结算所有商品
            checkout() {
                if (this.cart.length === 0) {
                    alert('购物车为空，无法结算');
                    return;
                }

                // 直接跳转到支付页面，传递购物车信息
                window.location.href = 'payment.html?type=cart';
            }

            // 显示消息
            showMessage(message, type = 'info') {
                // 创建消息元素
                const messageDiv = document.createElement('div');
                messageDiv.className = `cart-message cart-message-${type}`;
                messageDiv.textContent = message;
                messageDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 6px;
                    color: white;
                    font-weight: bold;
                    z-index: 10000;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                `;

                // 根据类型设置背景色
                switch (type) {
                    case 'success':
                        messageDiv.style.backgroundColor = '#28a745';
                        break;
                    case 'error':
                        messageDiv.style.backgroundColor = '#dc3545';
                        break;
                    case 'warning':
                        messageDiv.style.backgroundColor = '#ffc107';
                        messageDiv.style.color = '#333';
                        break;
                    default:
                        messageDiv.style.backgroundColor = '#17a2b8';
                }

                document.body.appendChild(messageDiv);

                // 显示动画
                setTimeout(() => {
                    messageDiv.style.opacity = '1';
                }, 100);

                // 3秒后自动移除
                setTimeout(() => {
                    messageDiv.style.opacity = '0';
                    setTimeout(() => {
                        if (messageDiv.parentNode) {
                            messageDiv.parentNode.removeChild(messageDiv);
                        }
                    }, 300);
                }, 3000);
            }

            // 获取购物车商品数量（供其他页面调用）
            getCartItemCount() {
                return this.cart.reduce((sum, item) => sum + item.quantity, 0);
            }
        }
    </script>
</body>
</html>