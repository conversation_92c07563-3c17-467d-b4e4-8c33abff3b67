<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金舟推荐好品 - 金舟国际物流</title>
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        /* Font Awesome 动画效果 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        header {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo-img {
            width: 45px;
            height: 45px;
            margin-right: 10px;
            border-radius: 8px;
        }

        .logo-text h1 {
            font-size: 22px;
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 12px;
            color: #666;
        }

        .gold {
            color: #d4af37;
        }

        /* Welcome Message */
        .welcome-message {
            background-color: rgba(255, 255, 255, 0.8);
            padding: 6px 15px;
            border-radius: 15px;
            font-size: 14px;
            color: #0c4da2;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            position: absolute;
            left: 300px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
        }

        .welcome-message i {
            margin-right: 5px;
            color: #d4af37;
        }

        .not-logged-in {
            color: #888;
        }

        /* 用户下拉菜单 */
        .user-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            padding: 10px 0;
            margin-top: 8px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }

        .user-dropdown.active {
            opacity: 1;
            visibility: visible;
        }

        .user-dropdown:before {
            content: '';
            position: absolute;
            top: -10px;
            left: 20px;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid white;
        }

        .user-dropdown-item {
            padding: 8px 15px;
            display: block;
            color: #333;
            font-size: 13px;
            text-decoration: none;
            transition: background-color 0.3s;
            text-align: left;
        }

        .user-dropdown-item:hover {
            background-color: #f5f5f5;
        }

        .user-dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 5px 0;
        }

        .user-dropdown-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        .user-dropdown-item.logout {
            color: #e74c3c;
        }

        /* Header Layout */
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #333;
        }

        .header-title h1 {
            font-size: 22px;
            margin-bottom: 2px;
            color: #0c4da2;
        }

        .header-title h1 i {
            color: #ffd700;
            margin-right: 8px;
        }

        .header-title p {
            font-size: 12px;
            color: #666;
            margin: 0;
        }

        /* 发布商品按钮样式 */
        .publish-button {
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
            color: #8b6914;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
            font-size: 14px;
            position: absolute;
            left: calc(50% + 220px);
            top: 50%;
            transform: translateY(-50%);
            display: inline-block;
        }

        .publish-button:hover {
            transform: translateY(calc(-50% - 2px));
            box-shadow: 0 6px 12px rgba(212, 175, 55, 0.4);
        }

        .publish-button i {
            margin-right: 8px;
        }

        /* 购物车徽章样式 */
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 20px;
            padding: 0 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .back-button {
            background: linear-gradient(135deg, #0c4da2 0%, #1e5bb8 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(12, 77, 162, 0.3);
            font-size: 14px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(12, 77, 162, 0.4);
        }

        .back-button i {
            margin-right: 8px;
        }

        /* Header Right Section */
        .header-right {
            display: flex;
            align-items: center;
        }

        /* Main Content */
        main {
            padding: 30px 0;
            min-height: calc(100vh - 150px);
        }

        /* Search Container */
        .search-container {
            margin: 25px 0 35px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            gap: 18px;
        }

        .hot-sales-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
            backdrop-filter: blur(10px);
            margin-right: 75px; /* 向左移动，增加右边距 */
        }

        .hot-sales-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
        }

        .hot-sales-button:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .hot-sales-button i {
            font-size: 16px;
            animation: fire-flicker 2s ease-in-out infinite alternate;
        }

        @keyframes fire-flicker {
            0% { transform: scale(1) rotate(-1deg); }
            50% { transform: scale(1.05) rotate(1deg); }
            100% { transform: scale(1) rotate(-1deg); }
        }

        /* 商品分类区域 - 右侧放置 */
        .category-section-right {
            display: flex;
            align-items: center;
            margin-left: 85px; /* 向右移动，增加左边距 */
        }

        /* 搜索框装饰元素 */
        .search-decoration {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0;
            pointer-events: none;
            animation: fadeInSlide 1s ease-out 0.5s forwards;
        }

        .search-decoration.left {
            left: 10%;
            animation: floatLeft 3s ease-in-out infinite;
        }

        .search-decoration.right {
            right: 10%;
            animation: floatRight 3s ease-in-out infinite reverse;
        }

        .decoration-icon {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.8);
            margin: 0 8px;
            display: inline-block;
            animation: pulse 2s ease-in-out infinite;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
            transition: all 0.3s ease;
        }

        .decoration-icon:hover {
            transform: scale(1.2);
            filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.5));
        }

        .decoration-icon:nth-child(2) {
            animation-delay: 0.5s;
        }

        .decoration-icon:nth-child(3) {
            animation-delay: 1s;
        }

        @keyframes floatLeft {
            0%, 100% { transform: translateY(-50%) translateX(0); }
            50% { transform: translateY(-50%) translateX(10px); }
        }

        @keyframes floatRight {
            0%, 100% { transform: translateY(-50%) translateX(0); }
            50% { transform: translateY(-50%) translateX(-10px); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 0.9; transform: scale(1.1); }
        }

        /* 添加闪烁效果 */
        .decoration-icon:nth-child(1) {
            animation: pulse 2s ease-in-out infinite, sparkle 4s ease-in-out infinite;
        }

        .decoration-icon:nth-child(2) {
            animation: pulse 2s ease-in-out infinite, sparkle 4s ease-in-out infinite;
            animation-delay: 0.5s, 1s;
        }

        .decoration-icon:nth-child(3) {
            animation: pulse 2s ease-in-out infinite, sparkle 4s ease-in-out infinite;
            animation-delay: 1s, 2s;
        }

        @keyframes sparkle {
            0%, 100% { filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3)); }
            25% { filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.6)); }
            50% { filter: drop-shadow(0 0 16px rgba(255, 255, 255, 0.8)); }
            75% { filter: drop-shadow(0 0 12px rgba(135, 206, 250, 0.6)); }
        }

        @keyframes fadeInSlide {
            0% {
                opacity: 0;
                transform: translateY(-50%) scale(0.8);
            }
            100% {
                opacity: 0.6;
                transform: translateY(-50%) scale(1);
            }
        }

        .search-box {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            max-width: 520px;
            width: 100%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .search-box:hover {
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.12);
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 1);
        }

        .search-box:focus-within {
            box-shadow: 0 16px 48px rgba(102, 126, 234, 0.15);
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 1);
        }



        .search-input {
            flex: 1;
            border: none;
            outline: none;
            padding: 14px 20px;
            font-size: 15px;
            background: transparent;
            color: #333;
            font-weight: 400;
        }

        .search-input::placeholder {
            color: #aaa;
            font-weight: 400;
        }

        .search-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 22px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 85px;
            justify-content: center;
        }

        .search-button:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: scale(1.02);
        }

        .search-button:active {
            transform: scale(0.98);
        }

        .search-button i {
            font-size: 13px;
        }

        /* 商品分类按钮样式 */
        .category-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 14px 20px;
            font-size: 14px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .category-button:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .category-button:active {
            transform: translateY(0);
        }

        .category-button i {
            font-size: 13px;
        }

        /* Search Results */
        .search-result-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px 25px;
            margin: 30px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .search-result-info p {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }

        .search-result-info strong {
            color: #FFD700;
        }

        .clear-search-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .clear-search-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        /* 主轮播图样式 */
        .main-carousel-container {
            margin-bottom: 40px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .main-carousel {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
            border-radius: 12px;
        }

        .carousel-slides {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.6s ease-in-out;
        }

        .carousel-slide {
            flex: 0 0 100%;
            width: 100%;
            height: 100%;
            position: relative;
            cursor: pointer;
            overflow: hidden;
        }

        .carousel-slide-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 40px;
            box-sizing: border-box;
        }

        /* 轮播图背景样式 */
        .carousel-slide.slide-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .carousel-slide.slide-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .carousel-slide.slide-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .carousel-slide.slide-4 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .carousel-slide.slide-5 {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        /* 轮播图文字内容 - 隐藏所有文字 */
        .slide-text {
            display: none;
        }

        .slide-title {
            display: none;
        }

        .slide-subtitle {
            display: none;
        }

        .slide-description {
            display: none;
        }

        /* 装饰元素 */
        .slide-decoration {
            position: absolute;
            right: 10%;
            top: 50%;
            transform: translateY(-50%);
            font-size: 200px;
            opacity: 0.1;
            z-index: 1;
        }

        /* 轮播导航按钮 */
        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            transition: all 0.3s ease;
            z-index: 10;
            backdrop-filter: blur(10px);
        }

        .carousel-nav:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-50%) scale(1.1);
        }

        .carousel-nav-prev {
            left: 30px;
        }

        .carousel-nav-next {
            right: 30px;
        }

        .carousel-nav:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: translateY(-50%) scale(1);
        }

        /* 轮播指示器 */
        .carousel-dots {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 12px;
            z-index: 10;
        }

        .carousel-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .carousel-dot.active {
            background: white;
            transform: scale(1.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .carousel-dot:hover {
            background: rgba(255, 255, 255, 0.7);
        }

        /* Product Grid */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-top: 40px;
        }

        /* Product Card */
        .product-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-image {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-card:hover .product-overlay {
            opacity: 1;
        }

        .quick-view-btn {
            background: #FFD700;
            color: #333;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-view-btn:hover {
            background: #FFC107;
            transform: scale(1.05);
        }

        .product-info {
            padding: 15px;
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .product-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0;
            line-height: 1.4;
            height: 44px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            flex: 1;
            margin-right: 8px;
        }

        .video-indicator {
            background: linear-gradient(135deg, #722ed1, #9254de);
            color: white;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 24px;
            height: 24px;
            flex-shrink: 0;
        }

        .video-indicator i {
            font-size: 10px;
        }

        .product-keywords {
            margin-bottom: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            min-height: 20px;
        }

        .keyword-tag-small {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
        }

        .keyword-tag-small.color-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .keyword-tag-small.color-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .keyword-tag-small.color-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .keyword-tag-small.color-4 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .keyword-tag-small.color-5 {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .keyword-more {
            color: #999;
            font-size: 10px;
            font-weight: 500;
            padding: 2px 4px;
        }

        .sales-text {
            color: #999;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .current-price {
            font-size: 18px;
            font-weight: 700;
            color: #e74c3c;
        }

        .original-price {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
        }

        .add-to-cart-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .add-to-cart-btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-1px);
        }

        .product-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .product-image {
            height: 200px;
            width: 100%;
            object-fit: cover;
            border-bottom: 1px solid #eee;
        }

        .product-info {
            padding: 20px;
        }

        .product-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #0c4da2;
        }

        .product-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }

        .product-price {
            font-size: 20px;
            color: #d4af37;
            font-weight: bold;
        }

        .tag {
            display: inline-block;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #8b6914;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        /* No Products Message */
        .no-products {
            text-align: center;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 8px;
            max-width: 400px;
            margin: 30px auto;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .no-products h2 {
            color: #0c4da2;
            font-size: 24px;
            margin-bottom: 15px;
        }

        .no-products p {
            color: #666;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .no-products i {
            font-size: 36px;
            color: #d4af37;
            margin-bottom: 0;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0 10px 0;
            gap: 20px;
            flex-wrap: wrap;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 12px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .pagination-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            min-width: 36px;
            text-align: center;
        }

        .pagination-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        .pagination-btn.active {
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            border-color: #d4af37;
            color: #333;
            font-weight: bold;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .pagination-btn:disabled:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            transform: none;
        }

        .pagination-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin: 0 10px;
        }

        .page-jump {
            display: flex;
            align-items: center;
            gap: 4px;
            margin: 0 10px;
        }

        .page-jump-label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 13px;
            white-space: nowrap;
        }

        .page-jump input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 6px 8px;
            border-radius: 4px;
            width: 60px;
            text-align: center;
            font-size: 14px;
        }

        .page-jump input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .page-jump input:focus {
            outline: none;
            border-color: #d4af37;
            background: rgba(255, 255, 255, 0.15);
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .page-size-selector label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 13px;
            white-space: nowrap;
        }

        .page-size-selector select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            min-width: 50px;
        }

        .page-size-selector select option {
            background: #333;
            color: white;
        }

        /* Footer */
        footer {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 10px;
        }

        footer .container {
            max-width: 500px;
        }

        footer p {
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Responsive Design */
        @media (min-width: 1400px) {
            .product-grid {
                grid-template-columns: repeat(5, 1fr);
                gap: 25px;
            }
        }

        @media (max-width: 1200px) {
            .product-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 18px;
            }
        }

        @media (max-width: 768px) {
            /* 分页响应式设计 */
            .pagination-container {
                flex-direction: column;
                gap: 15px;
                margin: 30px 0;
            }

            .pagination-controls {
                flex-direction: column;
                gap: 10px;
                padding: 8px 10px;
            }

            .pagination-info {
                font-size: 13px;
                margin: 0 5px;
            }

            .pagination-btn {
                padding: 6px 10px;
                font-size: 13px;
                min-width: 32px;
            }

            .page-size-selector label {
                font-size: 12px;
            }

            .page-size-selector select {
                padding: 3px 5px;
                font-size: 12px;
                min-width: 45px;
            }

            .page-jump {
                margin: 0 5px;
                gap: 3px;
            }

            .page-jump-label {
                font-size: 12px;
            }

            .page-jump input {
                width: 50px;
                padding: 4px 6px;
                font-size: 13px;
            }

            .header-title h1 {
                font-size: 20px;
            }

            .header-title p {
                font-size: 12px;
            }

            .search-container {
                margin: 18px 0 28px 0;
                padding: 0 15px;
                flex-direction: column;
                gap: 12px;
            }

            .hot-sales-button {
                padding: 10px 16px;
                font-size: 13px;
                margin-right: 48px;
            }

            .category-section-right {
                margin-left: 53px;
            }

            .search-decoration {
                display: none;
            }

            .search-box {
                max-width: 100%;
                border-radius: 25px;
            }

            .category-section-right {
                margin-left: 0;
                margin-top: 15px;
                justify-content: center;
            }

            .category-button {
                padding: 12px 18px;
                font-size: 13px;
                min-width: 100px;
                border-radius: 20px;
            }



            .search-input {
                padding: 12px 16px;
                font-size: 14px;
            }

            .search-button {
                padding: 12px 18px;
                font-size: 13px;
                min-width: 75px;
            }

            .product-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }

            .product-image {
                height: 150px;
            }

            .product-name {
                font-size: 14px;
                height: 36px;
            }

            .current-price {
                font-size: 16px;
            }

            /* 轮播图响应式 */
            .product-carousel-container {
                padding: 20px;
                margin-bottom: 30px;
            }

            .carousel-title {
                font-size: 24px;
                margin-bottom: 20px;
            }

            .carousel-item {
                flex: 0 0 250px;
            }

            .carousel-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .carousel-btn-prev {
                left: -20px;
            }

            .carousel-btn-next {
                right: -20px;
            }
        }

        @media (max-width: 480px) {
            .header-title {
                display: none;
            }

            .search-container {
                margin: 12px 0 22px 0;
                padding: 0 10px;
                flex-direction: column;
                gap: 10px;
            }

            .hot-sales-button {
                padding: 8px 14px;
                font-size: 12px;
                margin-right: 32px;
            }

            .category-section-right {
                margin-left: 38px;
            }

            .search-decoration {
                display: none;
            }

            .search-box {
                border-radius: 22px;
                max-width: 100%;
            }

            .category-section-right {
                margin-left: 0;
                margin-top: 12px;
                justify-content: center;
            }

            .category-button {
                padding: 10px 14px;
                font-size: 12px;
                min-width: 90px;
                border-radius: 18px;
            }



            .search-input {
                padding: 10px 14px;
                font-size: 13px;
            }

            .search-button {
                padding: 10px 14px;
                font-size: 12px;
                min-width: 65px;
            }

            .search-button span {
                display: none;
            }

            .product-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .product-image {
                height: 120px;
            }

            .product-info {
                padding: 10px;
            }

            .product-name {
                font-size: 13px;
                height: 32px;
            }

            .current-price {
                font-size: 15px;
            }

            .add-to-cart-btn {
                padding: 8px;
                font-size: 13px;
            }

            /* 移动端轮播图 */
            .product-carousel-container {
                padding: 15px;
                margin-bottom: 20px;
            }

            .carousel-title {
                font-size: 20px;
                margin-bottom: 15px;
            }

            .carousel-item {
                flex: 0 0 200px;
            }

            .carousel-item-content {
                padding: 15px;
            }

            .carousel-item-title {
                font-size: 16px;
            }

            .carousel-item-price {
                font-size: 18px;
            }

            .carousel-btn {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }

            .carousel-btn-prev {
                left: -15px;
            }

            .carousel-btn-next {
                right: -15px;
            }

            .carousel-indicator {
                width: 10px;
                height: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-left">
                <div class="logo">
                    <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                    <div class="logo-text">
                        <h1><span class="gold">金舟</span>国际物流</h1>
                        <p>Jin Zhou International Logistics</p>
                    </div>
                </div>
                
                <div class="welcome-message" id="welcomeMessage">
                    <i class="fas fa-user-circle"></i>
                    <span id="loginStatusText">正在加载...</span>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="user-dropdown-item account-info">
                            <i class="fas fa-user"></i><span id="accountName">我的账号</span>
                        </a>
                        <div class="user-dropdown-divider"></div>
                        <a href="#" class="user-dropdown-item logout" id="logoutButton">
                            <i class="fas fa-sign-out-alt"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>

            <div class="header-title">
                <h1><i class="fas fa-star"></i>金舟推荐好品</h1>
                <p>精选优质商品，为您推荐最佳选择</p>
            </div>

            <a href="my-products.html" class="publish-button">
                <i class="fas fa-box-open"></i>我的商品
                <span class="cart-badge" style="display: none;">0</span>
            </a>

            <div class="header-right">
            <a href="main.html" class="back-button">
                <i class="fas fa-arrow-left"></i>返回首页
            </a>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <!-- 搜索框 -->
            <div class="search-container">
              

                <!-- 热销榜按钮 -->
                <button class="hot-sales-button" onclick="goToHotSales()">
                    <i class="fas fa-fire"></i>
                    <span>热销榜</span>
                </button>

                <div class="search-box">
                    <input type="text" class="search-input" placeholder="请输入商品名称或关键词">
                    <button class="search-button">
                        <span>搜索</span>
                    </button>
                </div>

               

                <!-- 商品分类按钮 - 放在搜索框右侧 -->
                <div class="category-section-right">
                    <a href="product-categories-display.html" class="category-button">
                        <i class="fas fa-list"></i>
                        <span>商品分类</span>
                    </a>
                </div>
            </div>

            <!-- 主轮播图 -->
            <div class="main-carousel-container">
                <div class="main-carousel">
                    <div class="carousel-slides" id="carouselSlides">
                        <!-- 轮播图片将通过JavaScript动态添加 -->
                    </div>

                    <!-- 轮播控制按钮 -->
                    <button class="carousel-nav carousel-nav-prev" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="carousel-nav carousel-nav-next" id="nextBtn">
                        <i class="fas fa-chevron-right"></i>
                    </button>

                    <!-- 轮播指示器 -->
                    <div class="carousel-dots" id="carouselDots">
                        <!-- 指示器将通过JavaScript动态添加 -->
                    </div>
                </div>
            </div>

            <!-- 商品网格 -->
            <div class="product-grid" id="productGrid">
                <!-- 商品卡片将通过JavaScript动态添加 -->
            </div>

            <!-- 无商品提示 -->
            <div class="no-products" id="noProducts" style="display: none;">
                <i class="fas fa-box-open"></i>
                <h2>暂无推荐商品</h2>
                <p>目前还没有推荐的商品，请稍后再来查看。</p>
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <button class="pagination-btn" id="prevBtn" onclick="changePage(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <span class="pagination-info" id="pageInfo">第 1 页，共 1 页</span>

                <div class="pagination-controls">
                    <div class="page-jump">
                        <span class="page-jump-label">第:</span>
                        <input type="number" id="pageInput" min="1" max="1" placeholder="页码">
                        <span class="page-jump-label">页</span>
                        <button class="pagination-btn" onclick="jumpToPage()">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>

                    <div class="page-size-selector">
                        <label>每页:</label>
                        <select id="pageSizeSelect">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="30">30</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>

                <button class="pagination-btn" id="nextBtn" onclick="changePage(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </main>

    <footer>
        <div class="container" style="max-width: 800px;">
            <p>&copy; 2023 <span class="gold">金舟</span>国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // 请求管理器 - 防止重复请求和提供重试机制
        class RequestManager {
            constructor() {
                this.pendingRequests = new Map();
                this.retryCount = 3; // 最大重试次数
                this.retryDelay = 1000; // 重试延迟（毫秒）
            }

            async makeRequest(url, options = {}, retryAttempt = 0) {
                // 如果已有相同的请求在进行中，返回该请求的Promise
                if (this.pendingRequests.has(url)) {
                    console.log('请求已在进行中，等待结果:', url);
                    return this.pendingRequests.get(url);
                }

                // 创建新的请求Promise
                const requestPromise = this.performRequest(url, options, retryAttempt);

                // 将请求添加到pending列表
                this.pendingRequests.set(url, requestPromise);
                return requestPromise;
            }

            async performRequest(url, options, retryAttempt) {
                try {
                    const response = await fetch(url, options);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();

                    // 请求成功，从pending列表中移除
                    this.pendingRequests.delete(url);
                    return data;

                } catch (error) {
                    console.error(`请求失败 (尝试 ${retryAttempt + 1}/${this.retryCount + 1}):`, url, error.message);

                    // 如果是连接错误且还有重试次数，则重试
                    if (retryAttempt < this.retryCount && this.isRetryableError(error)) {
                        console.log(`${this.retryDelay}ms 后重试...`);
                        await this.delay(this.retryDelay);
                        return this.performRequest(url, options, retryAttempt + 1);
                    }

                    // 重试次数用完或不可重试的错误，从pending列表中移除
                    this.pendingRequests.delete(url);
                    throw error;
                }
            }

            isRetryableError(error) {
                // 判断是否为可重试的错误
                return error.message.includes('ERR_CONNECTION_CLOSED') ||
                       error.message.includes('ERR_CONNECTION_REFUSED') ||
                       error.message.includes('ERR_NETWORK') ||
                       error.message.includes('Failed to fetch');
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            // 取消所有pending请求
            cancelAllRequests() {
                this.pendingRequests.clear();
            }
        }

        // 全局请求管理器
        window.requestManager = new RequestManager();

        // 服务器状态检查器
        class ServerStatusChecker {
            constructor() {
                this.isServerOnline = false;
                this.checkInterval = null;
                this.checkIntervalMs = 30000; // 30秒检查一次
            }

            async checkServerStatus() {
                try {
                    const response = await fetch('/api/carousels', {
                        method: 'HEAD',
                        cache: 'no-cache'
                    });

                    if (response.ok || response.status === 404) {
                        // 服务器在线（即使返回404也说明服务器在运行）
                        if (!this.isServerOnline) {
                            this.isServerOnline = true;
                            this.onServerOnline();
                        }
                    } else {
                        throw new Error('Server not responding');
                    }
                } catch (error) {
                    if (this.isServerOnline) {
                        this.isServerOnline = false;
                        this.onServerOffline();
                    }
                }
            }

            onServerOnline() {
                console.log('服务器已连接');
                this.hideServerError();
                // 重新加载数据
                if (window.productManager && !window.productManager.isLoading) {
                    window.productManager.loadProductsFromServer();
                }
            }

            onServerOffline() {
                console.log('服务器连接断开');
                this.showServerError();
            }

            showServerError() {
                let errorDiv = document.getElementById('serverErrorNotification');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.id = 'serverErrorNotification';
                    errorDiv.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #ff4444;
                        color: white;
                        padding: 15px 20px;
                        border-radius: 5px;
                        z-index: 10000;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                        font-family: Arial, sans-serif;
                        max-width: 300px;
                    `;
                    errorDiv.innerHTML = `
                        <strong>服务器连接失败</strong><br>
                        请确保后端服务器正在运行<br>
                        <small>正在尝试重新连接...</small>
                    `;
                    document.body.appendChild(errorDiv);
                }
            }

            hideServerError() {
                const errorDiv = document.getElementById('serverErrorNotification');
                if (errorDiv) {
                    errorDiv.remove();
                }
            }

            startChecking() {
                this.checkServerStatus(); // 立即检查一次
                this.checkInterval = setInterval(() => {
                    this.checkServerStatus();
                }, this.checkIntervalMs);
            }

            stopChecking() {
                if (this.checkInterval) {
                    clearInterval(this.checkInterval);
                    this.checkInterval = null;
                }
            }
        }

        // 全局服务器状态检查器
        window.serverStatusChecker = new ServerStatusChecker();

        // 主轮播图功能
        class MainCarousel {
            constructor() {
                this.currentIndex = 0;
                this.totalSlides = 0;
                this.autoPlayInterval = null;
                this.autoPlayDelay = 5000; // 5秒自动切换
                this.isLoading = false; // 防止重复加载

                this.initElements();
                this.bindEvents();
                this.createSlides();
            }

            initElements() {
                this.slidesContainer = document.getElementById('carouselSlides');
                this.prevBtn = document.getElementById('prevBtn');
                this.nextBtn = document.getElementById('nextBtn');
                this.dotsContainer = document.getElementById('carouselDots');
            }

            bindEvents() {
                this.prevBtn.addEventListener('click', () => this.prevSlide());
                this.nextBtn.addEventListener('click', () => this.nextSlide());

                // 鼠标悬停时暂停自动播放
                const carousel = document.querySelector('.main-carousel');
                carousel.addEventListener('mouseenter', () => this.stopAutoPlay());
                carousel.addEventListener('mouseleave', () => this.startAutoPlay());

                // 键盘控制
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft') {
                        this.prevSlide();
                    } else if (e.key === 'ArrowRight') {
                        this.nextSlide();
                    }
                });
            }

            createSlides() {
                // 从服务器加载轮播图数据
                this.loadCarouselData();
            }

            loadCarouselData() {
                // 防止重复加载
                if (this.isLoading) {
                    console.log('轮播图数据正在加载中，跳过重复请求');
                    return;
                }

                this.isLoading = true;
                console.log('正在尝试加载轮播图数据...');

                window.requestManager.makeRequest('http://localhost:8080/api/carousels')
                .then(data => {
                    console.log('轮播图数据加载成功:', data);
                    if (data.success && data.carousels && data.carousels.length > 0) {
                        this.renderSlides(data.carousels);
                    } else {
                        this.showEmptyCarousel();
                    }
                })
                .catch(error => {
                    console.error('加载轮播图数据失败:', error);
                    this.showCarouselError();
                })
                .finally(() => {
                    this.isLoading = false;
                });
            }

            showEmptyCarousel() {
                const carouselContainer = document.querySelector('.main-carousel-container');
                carouselContainer.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; background: rgba(255, 255, 255, 0.9); border-radius: 12px;">
                        <i class="fas fa-images" style="font-size: 48px; color: #ddd;"></i>
                    </div>
                `;
            }

            showCarouselError() {
                const carouselContainer = document.querySelector('.main-carousel-container');
                carouselContainer.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; background: rgba(255, 255, 255, 0.9); border-radius: 12px;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #e74c3c;"></i>
                    </div>
                `;
            }

            renderSlides(slides) {
                this.totalSlides = slides.length;

                slides.forEach((slide, index) => {
                    const slideElement = document.createElement('div');
                    // 为自定义轮播图使用动态类名，为默认轮播图使用预设类名
                    const slideClass = slide.className || `slide-${(index % 5) + 1}`;
                    slideElement.className = `carousel-slide ${slideClass}`;

                    // 如果有自定义图片，使用图片背景
                    if (slide.image) {
                        slideElement.style.backgroundImage = `url(${slide.image})`;
                        slideElement.style.backgroundSize = 'cover';
                        slideElement.style.backgroundPosition = 'center';
                        slideElement.style.backgroundRepeat = 'no-repeat';
                    }

                    slideElement.innerHTML = `
                        <div class="carousel-slide-content">
                            <!-- 移除轮播图文字内容 -->
                        </div>
                    `;

                    slideElement.addEventListener('click', () => {
                        this.handleSlideClick(slide, index);
                    });

                    this.slidesContainer.appendChild(slideElement);
                });

                this.createDots();
                this.updateCarousel();
                this.startAutoPlay();
            }

            createDots() {
                for (let i = 0; i < this.totalSlides; i++) {
                    const dot = document.createElement('div');
                    dot.className = 'carousel-dot';
                    if (i === 0) dot.classList.add('active');

                    dot.addEventListener('click', () => {
                        this.goToSlide(i);
                    });

                    this.dotsContainer.appendChild(dot);
                }
            }

            updateCarousel() {
                const translateX = -this.currentIndex * 100;
                this.slidesContainer.style.transform = `translateX(${translateX}%)`;

                // 更新指示器
                document.querySelectorAll('.carousel-dot').forEach((dot, index) => {
                    dot.classList.toggle('active', index === this.currentIndex);
                });
            }

            nextSlide() {
                this.currentIndex = (this.currentIndex + 1) % this.totalSlides;
                this.updateCarousel();
            }

            prevSlide() {
                this.currentIndex = (this.currentIndex - 1 + this.totalSlides) % this.totalSlides;
                this.updateCarousel();
            }

            goToSlide(index) {
                this.currentIndex = index;
                this.updateCarousel();
            }

            startAutoPlay() {
                this.stopAutoPlay();
                this.autoPlayInterval = setInterval(() => {
                    this.nextSlide();
                }, this.autoPlayDelay);
            }

            stopAutoPlay() {
                if (this.autoPlayInterval) {
                    clearInterval(this.autoPlayInterval);
                    this.autoPlayInterval = null;
                }
            }

            handleSlideClick(slide, index) {
                console.log('点击了轮播图:', slide);

                // 如果轮播图有关联的商品，跳转到轮播图商品页面
                if (slide.products && slide.products.length > 0) {
                    window.open(`carousel-products.html?id=${slide.id}`, '_blank');
                } else if (slide.id) {
                    // 如果是轮播图但没有商品，也跳转到商品页面
                    window.open(`carousel-products.html?id=${slide.id}`, '_blank');
                } else {
                    // 移除文字提示，保持静默
                    console.log('轮播图点击事件');
                }
            }
        }

        // 商品数据管理
        class ProductManager {
            constructor() {
                this.products = [];
                this.allProducts = []; // 存储所有商品数据
                this.initialized = false;
                this.isLoading = false; // 防止重复加载
                // 从localStorage恢复页码设置，默认为1
                this.currentPage = parseInt(localStorage.getItem('currentPage')) || 1;
                // 从localStorage恢复页面大小设置，默认为20
                this.pageSize = parseInt(localStorage.getItem('pageSize')) || 20;
                this.totalPages = 1;
                this.currentFilters = {};
                // 保存搜索前的页码，用于清除搜索时恢复
                this.pageBeforeFilter = this.currentPage;
                this.initPagination();
            }

            // 初始化分页功能
            initPagination() {
                const pageSizeSelect = document.getElementById('pageSizeSelect');
                if (pageSizeSelect) {
                    // 设置当前保存的页面大小
                    pageSizeSelect.value = this.pageSize;

                    pageSizeSelect.addEventListener('change', (e) => {
                        this.pageSize = parseInt(e.target.value);
                        // 保存页面大小设置到localStorage
                        localStorage.setItem('pageSize', this.pageSize);
                        this.currentPage = 1;
                        // 保存页码到localStorage
                        localStorage.setItem('currentPage', this.currentPage);
                        // 重新计算总页数
                        this.totalPages = Math.ceil(this.allProducts.length / this.pageSize);
                        this.renderCurrentPage();
                        this.updatePagination();
                    });
                }

                // 页码输入框回车键支持
                const pageInput = document.getElementById('pageInput');
                if (pageInput) {
                    pageInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.jumpToPage();
                        }
                    });
                }
            }

            init() {
                // 确保页面大小选择器显示正确的值
                const pageSizeSelect = document.getElementById('pageSizeSelect');
                if (pageSizeSelect) {
                    pageSizeSelect.value = this.pageSize;
                }

                // 只有在没有URL参数时才加载默认商品
                const urlParams = new URLSearchParams(window.location.search);
                const hasUrlParams = urlParams.get('search') || urlParams.get('category') || urlParams.get('keyword') || urlParams.get('returnPage');

                if (!hasUrlParams) {
                    // 没有URL参数时，确保页码重置为第1页
                    this.currentPage = 1;
                    localStorage.setItem('currentPage', 1);
                    this.loadProductsFromServer();
                }
                this.initialized = true;
            }

            // 从服务器加载商品数据
            loadProductsFromServer(filters = {}) {
                const productGrid = document.getElementById('productGrid');
                const noProducts = document.getElementById('noProducts');

                // 检查是否有筛选条件
                const hasFilters = filters.category || filters.keyword || filters.search;
                const hadFilters = this.currentFilters && (this.currentFilters.category || this.currentFilters.keyword || this.currentFilters.search);





                if (hasFilters) {
                    // 如果之前没有筛选条件，保存当前页码
                    if (!hadFilters) {
                        this.pageBeforeFilter = this.currentPage;
                    }
                    // 有筛选条件时重置到第一页，除非是从URL恢复
                    if (!this.isRestoringFromUrl) {
                        this.currentPage = 1;
                        localStorage.setItem('currentPage', this.currentPage);
                    }
                } else if (hadFilters && !hasFilters) {
                    // 从有筛选条件变为无筛选条件，强制重置到第1页
                    // 这确保"显示全部商品"按钮始终回到第1页
                    this.currentPage = 1;
                    localStorage.setItem('currentPage', this.currentPage);
                    this.pageBeforeFilter = 1;
                }

                // 保存当前筛选条件
                this.currentFilters = filters;

                // 显示加载状态
                productGrid.innerHTML = '<div style="text-align: center; padding: 40px; color: #666; grid-column: 1 / -1;"><i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i><br>正在加载商品...</div>';

                // 构建查询参数
                const queryParams = new URLSearchParams();
                if (filters.category) queryParams.append('category', filters.category);
                if (filters.keyword) queryParams.append('keyword', filters.keyword);
                if (filters.search) queryParams.append('search', filters.search);

                const apiUrl = `/api/products${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

                // 防止重复请求
                if (this.isLoading) {
                    console.log('商品数据正在加载中，跳过重复请求');
                    return;
                }

                this.isLoading = true;

                // 从服务器获取商品列表
                window.requestManager.makeRequest(apiUrl)
                .then(data => {
                    if (data.success) {
                        const serverProducts = data.products || [];

                        // 转换服务器数据格式为前端需要的格式
                        this.allProducts = serverProducts.map(product => {
                            // 获取商品图片，优先使用images数组中的第一张图片，然后是mainImage，最后是默认图片
                            let productImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUwIiBoZWlnaHQ9IjI1MCIgdmlld0JveD0iMCAwIDI1MCAyNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyNTAiIGhlaWdodD0iMjUwIiBmaWxsPSIjZjBmMGYwIi8+Cjx0ZXh0IHg9IjEyNSIgeT0iMTI1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0iY2VudHJhbCIgZmlsbD0iIzk5OTk5OSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2Ij7mmoLml6Dlm77niYc8L3RleHQ+Cjwvc3ZnPgo=';

                            if (product.images && product.images.length > 0) {
                                productImage = product.images[0].url;
                            } else if (product.mainImage) {
                                productImage = product.mainImage;
                            }

                            return {
                                id: product.id,
                                name: product.name,
                                price: parseFloat(product.price),
                                originalPrice: parseFloat(product.price) * 1.3, // 假设原价是现价的1.3倍
                                image: productImage,
                                // 移除评分功能
                                sales: product.sales || Math.floor(Math.random() * 2000) + 100, // 使用真实销量或随机生成
                                keywords: product.keywords || [] // 包含关键词信息
                            };
                        });

                        // 计算分页信息
                        this.totalPages = Math.ceil(this.allProducts.length / this.pageSize);

                        // 确保当前页码不超过总页数
                        if (this.currentPage > this.totalPages && this.totalPages > 0) {
                            this.currentPage = this.totalPages;
                            localStorage.setItem('currentPage', this.currentPage);
                        }

                        // 如果有筛选条件但没有找到商品，显示特定提示
                        if ((filters.category || filters.keyword || filters.search) && this.allProducts.length === 0) {
                            let noProductMessage = '目前还没有推荐的商品，请稍后再来查看。';
                            if (filters.search) {
                                noProductMessage = `没有找到与"${filters.search}"相关的商品，请尝试其他关键词。`;
                            } else if (filters.keyword) {
                                noProductMessage = `没有找到与关键词"${filters.keyword}"相关的商品，请尝试其他关键词。`;
                            } else if (filters.categoryName) {
                                noProductMessage = `"${filters.categoryName}"分类下暂无商品，请查看其他分类。`;
                            }
                            this.showNoProducts(noProductMessage);
                            this.hidePagination();

                            // 显示筛选结果信息（0个商品）
                            this.showFilterResults(filters, 0);
                        } else {
                            this.renderCurrentPage();
                            this.updatePagination();

                            // 如果有筛选条件，显示筛选结果信息
                            if (filters.category || filters.keyword || filters.search) {
                                this.showFilterResults(filters, this.allProducts.length);
                            }
                        }

                        // 更新URL参数（除非是从URL恢复）
                        if (!this.isRestoringFromUrl) {
                            this.updateUrlParams(filters);
                        }
                    } else {
                        console.error('加载商品失败:', data.message);
                        this.showError('加载商品失败，请稍后重试');
                    }
                })
                .catch(error => {
                    console.error('加载商品失败:', error);
                    this.showError('网络错误，请检查网络连接');
                })
                .finally(() => {
                    this.isLoading = false;
                });
            }

            // 渲染当前页的商品
            renderCurrentPage() {
                const startIndex = (this.currentPage - 1) * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                this.products = this.allProducts.slice(startIndex, endIndex);
                this.renderProducts();

                // 控制轮播图显示：只在第1页显示
                this.toggleCarouselVisibility();
            }

            // 控制轮播图的显示和隐藏
            toggleCarouselVisibility() {
                const carouselContainer = document.querySelector('.main-carousel-container');
                if (carouselContainer) {
                    // 检查是否有筛选条件
                    const hasFilters = this.currentFilters && (
                        this.currentFilters.category ||
                        this.currentFilters.keyword ||
                        this.currentFilters.search
                    );

                    // 检查是否从商品分类页面跳转而来
                    const urlParams = new URLSearchParams(window.location.search);
                    const fromCategoryPage = urlParams.get('search') || urlParams.get('category') || urlParams.get('keyword');

                    // 轮播图显示条件：
                    // 1. 必须是第一页
                    // 2. 没有任何筛选条件
                    // 3. 不是从商品分类页面跳转而来
                    if (this.currentPage === 1 && !hasFilters && !fromCategoryPage) {
                        carouselContainer.style.display = 'block';
                    } else {
                        carouselContainer.style.display = 'none';
                    }
                }
            }

            // 更新URL参数
            updateUrlParams(filters = {}) {
                const url = new URL(window.location);

                // 清除所有相关参数
                url.searchParams.delete('search');
                url.searchParams.delete('category');
                url.searchParams.delete('categoryName');
                url.searchParams.delete('keyword');

                // 添加新的筛选参数
                if (filters.search) {
                    url.searchParams.set('search', filters.search);
                }
                if (filters.category) {
                    url.searchParams.set('category', filters.category);
                    if (filters.categoryName) {
                        url.searchParams.set('categoryName', filters.categoryName);
                    }
                }
                if (filters.keyword) {
                    url.searchParams.set('keyword', filters.keyword);
                }

                // 更新浏览器URL，但不刷新页面
                window.history.pushState({}, '', url);
            }

            // 更新分页组件
            updatePagination() {
                const paginationContainer = document.getElementById('paginationContainer');
                const pageInfo = document.getElementById('pageInfo');
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');
                const pageInput = document.getElementById('pageInput');

                if (this.allProducts.length > 0) {
                    paginationContainer.style.display = 'flex';

                    // 更新页面信息
                    pageInfo.textContent = `第 ${this.currentPage} 页，共 ${this.totalPages} 页`;

                    // 更新按钮状态
                    prevBtn.disabled = this.currentPage <= 1;
                    nextBtn.disabled = this.currentPage >= this.totalPages;

                    // 更新页码输入框
                    if (pageInput) {
                        pageInput.max = this.totalPages;
                        pageInput.placeholder = `1-${this.totalPages}`;
                        pageInput.value = ''; // 清空输入框
                    }
                } else {
                    this.hidePagination();
                }
            }

            // 隐藏分页组件
            hidePagination() {
                const paginationContainer = document.getElementById('paginationContainer');
                paginationContainer.style.display = 'none';
            }

            // 根据ID获取商品数据
            getProductById(productId) {
                return this.allProducts.find(product => product.id === productId);
            }

            // 切换页面
            changePage(direction) {
                const newPage = this.currentPage + direction;
                if (newPage >= 1 && newPage <= this.totalPages) {
                    this.currentPage = newPage;
                    // 保存页码到localStorage
                    localStorage.setItem('currentPage', this.currentPage);
                    this.renderCurrentPage();
                    this.updatePagination();

                    // 滚动到商品区域顶部
                    const productGrid = document.getElementById('productGrid');
                    productGrid.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }

            // 跳转到指定页面
            jumpToPage(pageNumber) {
                if (pageNumber === undefined) {
                    const pageInput = document.getElementById('pageInput');
                    pageNumber = parseInt(pageInput.value);
                }

                if (pageNumber >= 1 && pageNumber <= this.totalPages) {
                    this.currentPage = pageNumber;
                    // 保存页码到localStorage
                    localStorage.setItem('currentPage', this.currentPage);
                    this.renderCurrentPage();
                    this.updatePagination();

                    // 滚动到商品区域顶部
                    const productGrid = document.getElementById('productGrid');
                    productGrid.scrollIntoView({ behavior: 'smooth', block: 'start' });
                } else {
                    // 输入无效页码时的提示
                    const pageInput = document.getElementById('pageInput');
                    pageInput.value = '';
                    pageInput.placeholder = `请输入1-${this.totalPages}`;
                    setTimeout(() => {
                        pageInput.placeholder = `1-${this.totalPages}`;
                    }, 2000);
                }
            }

            // 显示筛选结果信息
            showFilterResults(filters, count) {
                // 移除之前的筛选结果提示（同时查找两种类名）
                const existingFilterResult = document.querySelector('.filter-result-info');
                const existingSearchResult = document.querySelector('.search-result-info');
                if (existingFilterResult) {
                    existingFilterResult.remove();
                }
                if (existingSearchResult) {
                    existingSearchResult.remove();
                }

                let filterText = '';
                if (filters.search) {
                    filterText = `搜索 "${filters.search}"`;
                } else if (filters.keyword) {
                    filterText = `关键词 "${filters.keyword}"`;
                } else if (filters.categoryName) {
                    filterText = `分类 "${filters.categoryName}"`;
                }

                // 创建筛选结果提示
                const resultInfo = document.createElement('div');
                resultInfo.className = 'filter-result-info search-result-info';
                resultInfo.innerHTML = `
                    <p>${filterText} 找到 <strong>${count}</strong> 个商品</p>
                    <button class="clear-search-btn" onclick="clearAllFilters()">显示全部商品</button>
                `;

                // 插入到商品网格前面
                const productGrid = document.getElementById('productGrid');
                productGrid.parentNode.insertBefore(resultInfo, productGrid);

                // 隐藏轮播图
                const carouselContainer = document.querySelector('.main-carousel-container');
                if (carouselContainer) {
                    carouselContainer.style.display = 'none';
                }
            }

            // 显示错误信息
            showError(message) {
                const productGrid = document.getElementById('productGrid');
                productGrid.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #e74c3c; grid-column: 1 / -1;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                        ${message}
                        <br><br>
                        <button onclick="location.reload()" style="background: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">重新加载</button>
                    </div>
                `;
            }

            renderProducts() {
                const productGrid = document.getElementById('productGrid');
                const noProducts = document.getElementById('noProducts');

                if (this.products.length === 0) {
                    productGrid.style.display = 'none';
                    noProducts.style.display = 'block';
                    return;
                }

                productGrid.style.display = 'grid';
                noProducts.style.display = 'none';
                productGrid.innerHTML = '';

                this.products.forEach(product => {
                    const productCard = this.createProductCard(product);
                    productGrid.appendChild(productCard);
                });
            }

            // 显示无商品状态
            showNoProducts(message = '目前还没有推荐的商品，请稍后再来查看。') {
                const productGrid = document.getElementById('productGrid');
                const noProducts = document.getElementById('noProducts');

                productGrid.style.display = 'none';
                noProducts.style.display = 'block';

                // 直接更新无商品区域的内容
                noProducts.innerHTML = `
                    <i class="fas fa-box-open" style="font-size: 48px; color: #d4af37; margin-bottom: 16px;"></i>
                    <h2 style="color: #0c4da2; font-size: 24px; margin-bottom: 15px;">暂无推荐商品</h2>
                    <p style="color: #666; font-size: 16px; margin: 0;">${message}</p>
                `;
            }

            createProductCard(product) {
                const card = document.createElement('div');
                card.className = 'product-card';
                card.style.cursor = 'pointer';

                // 生成关键词标签HTML
                const keywordsHtml = product.keywords && product.keywords.length > 0
                    ? `<div class="product-keywords">
                        ${product.keywords.slice(0, 3).map((keyword, index) =>
                            `<span class="keyword-tag-small color-${(index % 5) + 1}">${keyword.text}</span>`
                        ).join('')}
                        ${product.keywords.length > 3 ? `<span class="keyword-more">+${product.keywords.length - 3}</span>` : ''}
                       </div>`
                    : '';

                card.innerHTML = `
                    <div class="product-image">
                        <img src="${product.image}" alt="${product.name}" loading="lazy" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUwIiBoZWlnaHQ9IjI1MCIgdmlld0JveD0iMCAwIDI1MCAyNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyNTAiIGhlaWdodD0iMjUwIiBmaWxsPSIjZjBmMGYwIi8+Cjx0ZXh0IHg9IjEyNSIgeT0iMTI1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0iY2VudHJhbCIgZmlsbD0iIzk5OTk5OSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2Ij7mmoLml6Dlm77niYc8L3RleHQ+Cjwvc3ZnPgo='">
                        <div class="product-overlay">
                            <button class="quick-view-btn" onclick="event.stopPropagation(); viewProductDetail('${product.id}')">快速查看</button>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-header">
                            <h3 class="product-name">${product.name}</h3>
                            ${product.videoUrl ? '<div class="video-indicator" title="包含视频"><i class="fas fa-video"></i></div>' : ''}
                        </div>
                        ${keywordsHtml}
                        <div class="sales-text">(${product.sales}人已购买)</div>
                        <div class="product-price">
                            <span class="current-price">¥${product.price.toFixed(2)}</span>
                            <span class="original-price">¥${product.originalPrice.toFixed(2)}</span>
                        </div>
                        <button class="add-to-cart-btn" onclick="event.stopPropagation(); addToCart('${product.id}')">
                            加入购物车
                        </button>
                    </div>
                `;

                // 添加点击事件，点击卡片跳转到商品详情页
                card.addEventListener('click', () => {
                    const detailUrl = buildProductDetailUrl(product.id);
                    window.open(detailUrl, '_blank');
                });

                return card;
            }

            // 移除星星评分生成函数
        }

        // 搜索功能
        class SearchManager {
            constructor() {
                this.searchInput = document.querySelector('.search-input');
                this.searchButton = document.querySelector('.search-button');
                this.init();
            }

            init() {
                // 搜索按钮点击事件
                this.searchButton.addEventListener('click', () => {
                    this.performSearch();
                });

                // 输入框回车事件
                this.searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch();
                    }
                });

                // 输入框内容变化事件
                this.searchInput.addEventListener('input', (e) => {
                    const searchTerm = e.target.value.trim();
                    if (searchTerm === '') {
                        // 如果输入框被清空，恢复原始状态
                        this.clearSearch();
                    }
                });


            }

            performSearch() {
                const searchTerm = this.searchInput.value.trim();
                if (searchTerm) {
                    console.log('搜索关键词:', searchTerm);
                    // 使用新的API筛选功能
                    if (window.productManager) {
                        window.productManager.loadProductsFromServer({
                            search: searchTerm
                        });
                    }
                } else {
                    // 如果搜索框为空，恢复原始状态
                    this.clearSearch();
                }
            }

            // 保留原有的filterProducts方法作为备用
            filterProducts(searchTerm) {
                // 隐藏轮播图
                const carouselContainer = document.querySelector('.main-carousel-container');
                if (carouselContainer) {
                    carouselContainer.style.display = 'none';
                }

                // 获取所有商品卡片
                const productCards = document.querySelectorAll('.product-card');
                let visibleCount = 0;

                productCards.forEach(card => {
                    const productName = card.querySelector('.product-name').textContent.toLowerCase();
                    const isMatch = productName.includes(searchTerm.toLowerCase());

                    if (isMatch) {
                        card.style.display = 'block';
                        visibleCount++;
                    } else {
                        card.style.display = 'none';
                    }
                });

                // 显示搜索结果提示
                this.showSearchResults(searchTerm, visibleCount);
            }

            showSearchResults(searchTerm, count) {
                // 移除之前的搜索结果提示（同时查找两种类名）
                const existingSearchResult = document.querySelector('.search-result-info');
                const existingFilterResult = document.querySelector('.filter-result-info');
                if (existingSearchResult) {
                    existingSearchResult.remove();
                }
                if (existingFilterResult) {
                    existingFilterResult.remove();
                }

                // 创建搜索结果提示
                const resultInfo = document.createElement('div');
                resultInfo.className = 'search-result-info';
                resultInfo.innerHTML = `
                    <p>搜索 "<strong>${searchTerm}</strong>" 找到 <strong>${count}</strong> 个商品</p>
                    <button class="clear-search-btn">清除搜索</button>
                `;

                // 为清除按钮添加事件监听器
                const clearBtn = resultInfo.querySelector('.clear-search-btn');
                clearBtn.addEventListener('click', () => {
                    this.clearSearch();
                });

                // 插入到商品网格前面
                const productGrid = document.getElementById('productGrid');
                productGrid.parentNode.insertBefore(resultInfo, productGrid);
            }

            clearSearch() {
                // 清空搜索框
                this.searchInput.value = '';

                // 移除搜索结果提示（同时查找两种类名）
                const searchInfo = document.querySelector('.search-result-info');
                const filterInfo = document.querySelector('.filter-result-info');
                if (searchInfo) {
                    searchInfo.remove();
                }
                if (filterInfo) {
                    filterInfo.remove();
                }

                // 强制重置页码为第1页并清除localStorage缓存
                if (window.productManager) {
                    window.productManager.currentPage = 1;
                    localStorage.setItem('currentPage', 1);
                    window.productManager.pageBeforeFilter = 1;
                }

                // 清除URL参数，确保页面状态完全重置
                const url = new URL(window.location);
                url.searchParams.delete('search');
                url.searchParams.delete('category');
                url.searchParams.delete('categoryName');
                url.searchParams.delete('keyword');
                // 更新浏览器URL，不刷新页面
                window.history.replaceState({}, '', url);

                // 通过ProductManager重新加载商品，这样会正确控制轮播图显示
                if (window.productManager) {
                    window.productManager.loadProductsFromServer();
                }
            }


        }

        // 添加到购物车功能
        async function addToCart(productId) {
            console.log('添加商品到购物车:', productId);

            // 检查用户是否已登录
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data:', error);
            }

            if (!loggedInUser || !loggedInUser.isLoggedIn || !loggedInUser.username) {
                alert('请先登录后再添加商品到购物车');
                return;
            }

            try {
                // 获取商品数据
                const product = window.productManager ? window.productManager.getProductById(productId) : null;

                if (!product) {
                    alert('商品信息获取失败');
                    return;
                }

                // 调用购物车API
                const response = await fetch('/api/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: loggedInUser.username,
                        productId: productId,
                        productData: {
                            name: product.name,
                            price: product.price,
                            mainImage: product.mainImage || (product.images && product.images.length > 0 ? product.images[0].url : ''),
                            description: product.description || '',
                            stock: product.stock || 0
                        }
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 显示成功消息
                    showCartMessage('商品已添加到购物车！', 'success');

                    // 更新购物车图标数量（如果存在）
                    updateCartBadge();
                } else {
                    alert(data.message || '添加到购物车失败');
                }
            } catch (error) {
                console.error('添加到购物车失败:', error);
                alert('添加到购物车失败，请稍后重试');
            }
        }

        // 显示购物车消息
        function showCartMessage(message, type = 'info') {
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = `cart-message cart-message-${type}`;
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            // 根据类型设置背景色
            switch (type) {
                case 'success':
                    messageDiv.style.backgroundColor = '#28a745';
                    break;
                case 'error':
                    messageDiv.style.backgroundColor = '#dc3545';
                    break;
                case 'warning':
                    messageDiv.style.backgroundColor = '#ffc107';
                    messageDiv.style.color = '#333';
                    break;
                default:
                    messageDiv.style.backgroundColor = '#17a2b8';
            }

            document.body.appendChild(messageDiv);

            // 显示动画
            setTimeout(() => {
                messageDiv.style.opacity = '1';
            }, 100);

            // 3秒后自动移除
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // 更新购物车徽章数量
        async function updateCartBadge() {
            try {
                let loggedInUser = null;
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }

                if (!loggedInUser || !loggedInUser.username) {
                    return;
                }

                const response = await fetch(`/api/cart/${encodeURIComponent(loggedInUser.username)}`);
                const data = await response.json();

                if (data.success && data.cart) {
                    const totalItems = data.cart.reduce((sum, item) => sum + item.quantity, 0);

                    // 查找购物车徽章元素并更新
                    const cartBadge = document.querySelector('.cart-badge');
                    if (cartBadge) {
                        if (totalItems > 0) {
                            cartBadge.textContent = totalItems;
                            cartBadge.style.display = 'block';
                        } else {
                            cartBadge.style.display = 'none';
                        }
                    }
                }
            } catch (error) {
                console.error('更新购物车徽章失败:', error);
            }
        }

        // 清除搜索功能（全局函数）
        function clearSearch() {
            const searchManager = window.searchManager;
            if (searchManager) {
                searchManager.clearSearch();
            }
        }

        // 处理URL参数
        function handleUrlParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            const searchKeyword = urlParams.get('search');
            const categoryId = urlParams.get('category');
            const categoryName = urlParams.get('categoryName');
            const returnPage = urlParams.get('returnPage');

            // 处理返回页码（从商品详情页返回时）
            if (returnPage) {
                const pageNumber = parseInt(returnPage);
                if (pageNumber > 0 && window.productManager) {
                    window.productManager.currentPage = pageNumber;
                    localStorage.setItem('currentPage', pageNumber);
                }
                // 清除returnPage参数，避免影响后续操作
                urlParams.delete('returnPage');
                const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
                window.history.replaceState({}, '', newUrl);
            } else if (searchKeyword || categoryId || categoryName) {
                // 如果有任何筛选参数但没有returnPage，强制重置页码为第1页
                if (window.productManager) {
                    window.productManager.currentPage = 1;
                    localStorage.setItem('currentPage', 1);
                }
            }

            if (searchKeyword) {
                // 如果有搜索关键词，使用新的API筛选
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.value = searchKeyword;
                }

                // 使用新的筛选方式，页码已经重置为第1页
                if (window.productManager) {
                    // 标记这是从URL参数恢复的搜索，但页码已经重置
                    window.productManager.isRestoringFromUrl = true;
                    window.productManager.loadProductsFromServer({
                        search: searchKeyword
                    });
                    window.productManager.isRestoringFromUrl = false;
                }
            } else if (categoryId && categoryName) {
                // 如果有分类信息，按分类筛选
                if (window.productManager) {
                    // 标记这是从URL参数恢复的筛选，但页码已经重置
                    window.productManager.isRestoringFromUrl = true;
                    window.productManager.loadProductsFromServer({
                        category: categoryId,
                        categoryName: categoryName
                    });
                    window.productManager.isRestoringFromUrl = false;
                }
            } else if (returnPage) {
                // 如果只有returnPage参数（从商品详情页返回到全部商品页面）
                if (window.productManager) {
                    // 加载所有商品，但保持指定的页码
                    window.productManager.loadProductsFromServer();
                }
            }

            // 保留URL参数，这样用户刷新页面时仍能看到筛选结果
            // returnPage参数已经被处理并清除，避免状态不一致
        }

        // 清除所有筛选
        function clearAllFilters() {
            // 清空搜索框
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
            }

            // 移除筛选结果提示（同时查找两种类名）
            const filterInfo = document.querySelector('.filter-result-info');
            const searchInfo = document.querySelector('.search-result-info');
            if (filterInfo) {
                filterInfo.remove();
            }
            if (searchInfo) {
                searchInfo.remove();
            }

            // 强制重置页码为第1页并清除localStorage缓存
            if (window.productManager) {
                window.productManager.currentPage = 1;
                localStorage.setItem('currentPage', 1);
                // 清除筛选前的页码缓存
                window.productManager.pageBeforeFilter = 1;
            }

            // 清除URL参数，确保页面状态完全重置
            const url = new URL(window.location);
            url.searchParams.delete('search');
            url.searchParams.delete('category');
            url.searchParams.delete('categoryName');
            url.searchParams.delete('keyword');
            // 更新浏览器URL，不刷新页面
            window.history.replaceState({}, '', url);

            // 重新加载所有商品
            if (window.productManager) {
                window.productManager.loadProductsFromServer();
            }
        }

        // 全局函数，供按钮调用
        window.clearAllFilters = clearAllFilters;

        // 检查用户登录状态
        document.addEventListener('DOMContentLoaded', function() {
            // 启动服务器状态检查
            window.serverStatusChecker.startChecking();

            // 初始化轮播图
            new MainCarousel();

            // 初始化商品管理器并设为全局变量
            window.productManager = new ProductManager();

            // 初始化搜索管理器
            window.searchManager = new SearchManager();

            // 先初始化ProductManager，然后处理URL参数
            window.productManager.init();

            // 初始化购物车徽章
            updateCartBadge();

            // 确保页面大小选择器显示正确的值（在DOM完全加载后）
            setTimeout(() => {
                const pageSizeSelect = document.getElementById('pageSizeSelect');
                if (pageSizeSelect && window.productManager) {
                    pageSizeSelect.value = window.productManager.pageSize;
                }
            }, 100);

            // 检查URL参数，处理从分类页面跳转过来的情况
            handleUrlParameters();
            const welcomeMessage = document.getElementById('welcomeMessage');
            const loginStatusText = document.getElementById('loginStatusText');
            const userDropdown = document.getElementById('userDropdown');
            const accountName = document.getElementById('accountName');
            const logoutButton = document.getElementById('logoutButton');
            
            let dropdownTimeout;
            
            // 添加鼠标事件来控制下拉菜单的显示和隐藏
            if (welcomeMessage && userDropdown) {
                // 鼠标进入欢迎信息区域显示下拉菜单
                welcomeMessage.addEventListener('mouseenter', function() {
                    clearTimeout(dropdownTimeout);
                    userDropdown.classList.add('active');
                });
                
                // 鼠标离开欢迎信息区域0.01秒后隐藏下拉菜单
                welcomeMessage.addEventListener('mouseleave', function() {
                    clearTimeout(dropdownTimeout);
                    dropdownTimeout = setTimeout(() => {
                        userDropdown.classList.remove('active');
                    }, 10); // 0.01秒延迟
                });
                
                // 鼠标进入下拉菜单时清除隐藏计时器
                userDropdown.addEventListener('mouseenter', function() {
                    clearTimeout(dropdownTimeout);
                });
                
                // 鼠标离开下拉菜单0.01秒后隐藏
                userDropdown.addEventListener('mouseleave', function() {
                    clearTimeout(dropdownTimeout);
                    dropdownTimeout = setTimeout(() => {
                        userDropdown.classList.remove('active');
                    }, 10); // 0.01秒延迟
                });
            }
            
            // 安全地获取登录信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data from sessionStorage:', error);
                sessionStorage.removeItem('loggedInUser');
            }

            // 更新欢迎信息
            if (loggedInUser && loggedInUser.isLoggedIn && loggedInUser.username) {
                // 检查用户是否被拉黑
                BlacklistHandler.checkUserBlacklisted();
                
                // 定期检查用户状态
                setInterval(BlacklistHandler.checkUserBlacklisted, 30000);
                
                // 用户已登录
                loginStatusText.textContent = '欢迎，' + loggedInUser.username;
                welcomeMessage.classList.remove('not-logged-in');
                
                // 更新下拉菜单中的用户名
                accountName.textContent = "我的账户";
                
                // 删除直接跳转到用户中心的点击事件
                welcomeMessage.style.cursor = 'pointer';
                
                // 为"我的账户"添加点击事件，跳转到用户中心
                document.querySelector('.account-info').addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = 'dashboard.html'; // 假设用户中心页面是dashboard.html
                });
                
                // 处理退出登录
                logoutButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    sessionStorage.removeItem('loggedInUser');
                    window.location.reload();
                });
            } else {
                // 用户未登录
                loginStatusText.innerHTML = '<a href="login.html?returnUrl=' + encodeURIComponent(window.location.href) + '" style="color: inherit; text-decoration: none;">未登录</a>';
                welcomeMessage.classList.add('not-logged-in');
                welcomeMessage.style.cursor = 'pointer';
                welcomeMessage.addEventListener('click', function() {
                    window.location.href = 'login.html?returnUrl=' + encodeURIComponent(window.location.href);
                });
                
                // 隐藏下拉菜单
                userDropdown.style.display = "none";
            }
        });

        // 全局函数
        window.viewProductDetail = function(productId) {
            const detailUrl = buildProductDetailUrl(productId);
            window.open(detailUrl, '_blank');
        };

        // 构建商品详情页URL，保留当前页面的搜索参数和页码
        function buildProductDetailUrl(productId) {
            const currentParams = new URLSearchParams(window.location.search);
            const detailParams = new URLSearchParams();

            // 添加商品ID和来源
            detailParams.set('id', productId);
            detailParams.set('from', 'recommend');

            // 保留搜索相关的参数
            const searchParams = ['search', 'category', 'categoryName', 'keyword'];
            searchParams.forEach(param => {
                const value = currentParams.get(param);
                if (value) {
                    detailParams.set(param, value);
                }
            });

            // 保留当前页码信息
            if (window.productManager && window.productManager.currentPage > 1) {
                detailParams.set('page', window.productManager.currentPage);
            }

            return `product-detail.html?${detailParams.toString()}`;
        }

        // 分页函数
        window.changePage = function(direction) {
            if (window.productManager) {
                window.productManager.changePage(direction);
            }
        };

        // 页码跳转函数
        window.jumpToPage = function(pageNumber) {
            if (window.productManager) {
                window.productManager.jumpToPage(pageNumber);
            }
        };

        // 跳转到热销榜页面
        window.goToHotSales = function() {
            window.location.href = 'hot-sales.html';
        };

        window.addToCart = function(productId) {
            // 调用上面定义的addToCart函数
            addToCart(productId);
        };
    </script>
</body>
</html>